name: Package test on Python 3.8-3.13 on Win/Linux/macOS

on:
  workflow_dispatch:

jobs:
  test-python:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest, macos-13]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]
        exclude:
          - os: macos-latest
            python-version: "3.8"
          - os: macos-latest
            python-version: "3.9"
          - os: macos-13
            python-version: "3.10"
          - os: macos-13
            python-version: "3.11"
          - os: macos-13
            python-version: "3.12"
          - os: macos-13
            python-version: "3.13"
      max-parallel: 3
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install flake8 pytest ruff
          python -m pip install .
      - name: Lint with flake8 and ruff
        run: |
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          ruff check .
          ruff format --check
      - name: Test with pytest
        env:
          DOC2X_APIKEY: ${{ secrets.DOC2X_APIKEY }}
          PYTHONIOENCODING: utf-8
        run: |
          pytest -s
