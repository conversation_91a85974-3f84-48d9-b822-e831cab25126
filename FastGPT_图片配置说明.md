# FastGPT 图片本地化配置说明

## 1. Docker配置修改

在您的 `docker-compose.yml` 文件中，找到 `fastgpt` 服务部分，添加图片目录映射：

```yaml
fastgpt:
  container_name: fastgpt
  image: ghcr.io/labring/fastgpt:v4.11.0
  ports:
    - 3000:3000
  networks:
    - fastgpt
  depends_on:
    - mongo
    - sandbox
    - pg
  restart: always
  volumes:
    - ./config.json:/app/data/config.json
    # 添加这一行 - 关键修改
    - /home/<USER>/llm_project/fastgpt/images:/app/static/images
  environment:
    # ... 其他环境变量保持不变
```

## 2. 目录结构

确保以下目录存在并有正确权限：

```
/home/<USER>/llm_project/fastgpt/
├── images/                    # 图片存储目录
├── docker-compose.yml         # Docker配置文件
└── config.json               # FastGPT配置文件
```

## 3. 创建图片目录

```bash
# 创建图片目录
sudo mkdir -p /home/<USER>/llm_project/fastgpt/images

# 设置权限（确保Docker容器可以访问）
sudo chmod 755 /home/<USER>/llm_project/fastgpt/images
sudo chown -R aiuser:aiuser /home/<USER>/llm_project/fastgpt/images
```

## 4. 图片路径映射关系

| 位置 | 路径 | 说明 |
|------|------|------|
| 宿主机 | `/home/<USER>/llm_project/fastgpt/images/` | 实际存储位置 |
| 容器内 | `/app/static/images/` | Docker容器内路径 |
| Web访问 | `/static/images/` | 前端访问路径 |

## 5. 图片处理流程

### 原始Markdown中的图片：
```markdown
![图1-1](https://example.com/image.jpg)
```

### 处理后的路径：
```markdown
![图1-1](/static/images/document_a1b2c3d4e5f6.jpg)
```

### 实际文件位置：
```
/home/<USER>/llm_project/fastgpt/images/document_a1b2c3d4e5f6.jpg
```

## 6. 使用图片处理脚本

### 安装依赖
```bash
pip install pdfdeal
```

### 处理单个文件
```python
from fastgpt_image_processor import FastGPTImageProcessor

processor = FastGPTImageProcessor(
    host_image_dir="/home/<USER>/llm_project/fastgpt/images",
    web_access_path="/static/images"
)

# 处理单个Markdown文件
processor.process_single_markdown("your_document.md")
```

### 批量处理
```python
# 批量处理目录中的所有Markdown文件
success_files, failed_files, overall = processor.process_multiple_markdowns("./docs/")
```

## 7. 重启FastGPT

修改Docker配置后，需要重启服务：

```bash
# 停止服务
docker-compose down

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f fastgpt
```

## 8. 验证配置

### 检查图片目录映射
```bash
# 进入FastGPT容器
docker exec -it fastgpt bash

# 检查静态目录
ls -la /app/static/images/
```

### 测试图片访问
在浏览器中访问：
```
http://localhost:3000/static/images/your_image.jpg
```

## 9. 常见问题

### 问题1：图片无法显示
- 检查文件权限
- 确认Docker映射路径正确
- 查看FastGPT日志

### 问题2：路径不正确
- 确认Web访问路径为 `/static/images/`
- 检查Markdown中的图片路径格式

### 问题3：容器无法访问图片目录
```bash
# 检查目录权限
ls -la /home/<USER>/llm_project/fastgpt/

# 修改权限
sudo chown -R 1000:1000 /home/<USER>/llm_project/fastgpt/images
```

## 10. 最佳实践

1. **文件命名**：使用MD5哈希避免文件名冲突
2. **权限管理**：确保Docker容器有读写权限
3. **备份**：定期备份图片目录
4. **清理**：定期清理不再使用的图片文件
5. **监控**：监控图片目录大小，避免磁盘空间不足

## 11. 完整示例

假设您有一个包含图片的Markdown文件：

```markdown
# 智能节水系统文档

![图1-1 TFEMJG-2 型 智能节水系统](https://example.com/system.jpg)

系统介绍...
```

处理后将变成：

```markdown
# 智能节水系统文档

![图1-1 TFEMJG-2 型 智能节水系统](/static/images/document_abc123def456.jpg)

系统介绍...
```

在FastGPT中，这个图片将能够正常显示和渲染。
