#!/bin/bash

# FastGPT图片配置检查脚本

echo "=========================================="
echo "FastGPT 图片配置检查脚本"
echo "=========================================="

# 配置变量
HOST_IMAGE_DIR="/home/<USER>/llm_project/fastgpt/images"
IMAGE_FILE="1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg"
CONTAINER_NAME="fastgpt"
FASTGPT_URL="http://localhost:3000"

echo ""
echo "1. 检查宿主机图片文件..."
if [ -f "$HOST_IMAGE_DIR/$IMAGE_FILE" ]; then
    echo "✅ 宿主机图片文件存在: $HOST_IMAGE_DIR/$IMAGE_FILE"
    echo "   文件大小: $(du -h "$HOST_IMAGE_DIR/$IMAGE_FILE" | cut -f1)"
    echo "   文件权限: $(ls -la "$HOST_IMAGE_DIR/$IMAGE_FILE" | cut -d' ' -f1)"
else
    echo "❌ 宿主机图片文件不存在: $HOST_IMAGE_DIR/$IMAGE_FILE"
    echo "   请确认文件路径是否正确"
    exit 1
fi

echo ""
echo "2. 检查图片目录权限..."
DIR_PERMISSIONS=$(ls -ld "$HOST_IMAGE_DIR" | cut -d' ' -f1)
DIR_OWNER=$(ls -ld "$HOST_IMAGE_DIR" | cut -d' ' -f3-4)
echo "   目录权限: $DIR_PERMISSIONS"
echo "   目录所有者: $DIR_OWNER"

echo ""
echo "3. 检查FastGPT容器状态..."
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ FastGPT容器正在运行"
    
    # 检查容器内的静态目录
    echo ""
    echo "4. 检查容器内静态目录..."
    if docker exec "$CONTAINER_NAME" sh -c "test -d /app/static"; then
        echo "✅ 容器内 /app/static 目录存在"
    else
        echo "❌ 容器内 /app/static 目录不存在"
    fi
    
    if docker exec "$CONTAINER_NAME" sh -c "test -d /app/static/images"; then
        echo "✅ 容器内 /app/static/images 目录存在"
    else
        echo "❌ 容器内 /app/static/images 目录不存在"
        echo "   请检查Docker卷映射配置"
    fi
    
    # 检查具体图片文件
    echo ""
    echo "5. 检查容器内图片文件..."
    if docker exec "$CONTAINER_NAME" sh -c "test -f /app/static/images/$IMAGE_FILE"; then
        echo "✅ 容器内图片文件存在: /app/static/images/$IMAGE_FILE"
        
        # 获取文件信息
        FILE_SIZE=$(docker exec "$CONTAINER_NAME" sh -c "ls -la /app/static/images/$IMAGE_FILE" | awk '{print $5}')
        echo "   容器内文件大小: $FILE_SIZE 字节"
    else
        echo "❌ 容器内图片文件不存在: /app/static/images/$IMAGE_FILE"
        echo "   请检查Docker卷映射配置"
    fi
    
    # 列出容器内images目录的所有文件
    echo ""
    echo "6. 容器内images目录文件列表:"
    docker exec "$CONTAINER_NAME" sh -c "ls -la /app/static/images/" 2>/dev/null || echo "   目录为空或不存在"
    
else
    echo "❌ FastGPT容器未运行"
    echo "   请启动容器: docker-compose up -d"
    exit 1
fi

echo ""
echo "7. 检查Docker卷映射..."
MOUNT_INFO=$(docker inspect "$CONTAINER_NAME" | grep -A 5 -B 5 "static/images" 2>/dev/null)
if [ -n "$MOUNT_INFO" ]; then
    echo "✅ 找到static/images相关的卷映射"
    echo "$MOUNT_INFO"
else
    echo "❌ 未找到static/images的卷映射"
    echo "   请检查docker-compose.yml配置"
fi

echo ""
echo "8. 测试HTTP访问..."
HTTP_URL="$FASTGPT_URL/static/images/$IMAGE_FILE"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HTTP_URL" 2>/dev/null)

if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ HTTP访问成功: $HTTP_URL"
    echo "   状态码: $HTTP_STATUS"
else
    echo "❌ HTTP访问失败: $HTTP_URL"
    echo "   状态码: $HTTP_STATUS"
    
    # 检查FastGPT服务是否响应
    FASTGPT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$FASTGPT_URL" 2>/dev/null)
    if [ "$FASTGPT_STATUS" = "200" ]; then
        echo "   FastGPT服务正常，但图片访问失败"
    else
        echo "   FastGPT服务可能未正常启动，状态码: $FASTGPT_STATUS"
    fi
fi

echo ""
echo "=========================================="
echo "检查完成！"
echo ""
echo "📋 总结:"
echo "   宿主机文件: $([ -f "$HOST_IMAGE_DIR/$IMAGE_FILE" ] && echo "存在" || echo "不存在")"
echo "   容器运行: $(docker ps | grep -q "$CONTAINER_NAME" && echo "正常" || echo "异常")"
echo "   容器内文件: $(docker exec "$CONTAINER_NAME" sh -c "test -f /app/static/images/$IMAGE_FILE" 2>/dev/null && echo "存在" || echo "不存在")"
echo "   HTTP访问: $([ "$HTTP_STATUS" = "200" ] && echo "成功" || echo "失败")"
echo ""
echo "🔗 FastGPT中使用的路径:"
echo "   /static/images/$IMAGE_FILE"
echo ""
echo "🌐 浏览器直接访问:"
echo "   $HTTP_URL"
echo "=========================================="
