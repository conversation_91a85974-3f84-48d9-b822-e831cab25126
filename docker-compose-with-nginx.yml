# 在您现有的docker-compose.yml基础上添加以下服务

  # 静态文件服务 - 添加到services部分
  nginx-static:
    image: nginx:alpine
    container_name: nginx-static
    restart: always
    ports:
      - "8080:8080"  # 静态文件服务端口
    networks:
      - fastgpt
    volumes:
      - /home/<USER>/llm_project/fastgpt/images:/usr/share/nginx/html/images:ro  # 只读映射
      - ./nginx-static-files.conf:/etc/nginx/conf.d/default.conf:ro
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8080/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # 修改fastgpt服务，添加环境变量指向静态文件服务
  fastgpt:
    container_name: fastgpt
    image: ghcr.io/labring/fastgpt:v4.11.0
    ports:
      - 3000:3000
    networks:
      - fastgpt
    depends_on:
      - mongo
      - sandbox
      - pg
      - nginx-static  # 添加依赖
    restart: always
    volumes:
      - ./config.json:/app/data/config.json
      # 可以保留原来的映射，也可以移除
      # - /home/<USER>/llm_project/fastgpt/images:/app/static/images
    environment:
      # 添加静态文件服务的URL
      - STATIC_FILE_BASE_URL=http://nginx-static:8080
      # 其他环境变量保持不变...
      - FE_DOMAIN=
      - DEFAULT_ROOT_PSW=1234
      - TOKEN_KEY=any
      - ROOT_KEY=root_key
      - FILE_TOKEN_KEY=filetoken
      - AES256_SECRET_KEY=fastgptkey
      - PLUGIN_BASE_URL=http://fastgpt-plugin:3000
      - PLUGIN_TOKEN=xxxxxx
      - SANDBOX_URL=http://sandbox:3000
      - AIPROXY_API_ENDPOINT=http://aiproxy:3000
      - AIPROXY_API_TOKEN=aiproxy
      - DB_MAX_LINK=30
      - MONGODB_URI=********************************************************************
      - REDIS_URL=redis://default:mypassword@redis:6379
      - PG_URL=**************************************/postgres
      - LOG_LEVEL=info
      - STORE_LOG_LEVEL=warn
      - WORKFLOW_MAX_RUN_TIMES=1000
      - WORKFLOW_MAX_LOOP_TIMES=100
      - CHAT_FILE_EXPIRE_TIME=7
