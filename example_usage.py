#!/usr/bin/env python3
"""
FastGPT图片处理使用示例
"""

from fastgpt_image_processor import FastGPTImageProcessor
import os

def example_single_file():
    """处理单个Markdown文件的示例"""
    print("=== 单文件处理示例 ===")
    
    # 初始化处理器
    processor = FastGPTImageProcessor(
        host_image_dir="/home/<USER>/llm_project/fastgpt/images",
        web_access_path="/static/images",
        fastgpt_domain=""  # 使用相对路径，适合Docker环境
    )
    
    # 处理单个文件
    md_file = "example.md"  # 替换为您的实际文件路径
    
    if os.path.exists(md_file):
        success = processor.process_single_markdown(
            md_file_path=md_file,
            download_online_images=True  # 下载在线图片
        )
        
        if success:
            print(f"✅ 成功处理文件: {md_file}")
        else:
            print(f"❌ 处理文件时出现问题: {md_file}")
    else:
        print(f"文件不存在: {md_file}")

def example_batch_processing():
    """批量处理Markdown文件的示例"""
    print("\n=== 批量处理示例 ===")
    
    # 初始化处理器
    processor = FastGPTImageProcessor(
        host_image_dir="/home/<USER>/llm_project/fastgpt/images",
        web_access_path="/static/images",
        fastgpt_domain=""
    )
    
    # 批量处理目录
    md_directory = "./markdown_docs"  # 替换为您的实际目录路径
    
    if os.path.exists(md_directory):
        success_files, failed_files, overall_success = processor.process_multiple_markdowns(
            md_directory=md_directory,
            download_online_images=True,
            threads=2  # 并发线程数
        )
        
        print(f"处理结果:")
        print(f"  成功文件数: {len(success_files)}")
        print(f"  失败文件数: {len(failed_files)}")
        print(f"  整体状态: {'成功' if overall_success else '部分失败'}")
        
        if failed_files:
            print("失败的文件:")
            for failed in failed_files:
                print(f"  - {failed}")
    else:
        print(f"目录不存在: {md_directory}")

def create_test_markdown():
    """创建一个测试用的Markdown文件"""
    print("\n=== 创建测试文件 ===")
    
    test_content = """# 测试文档

这是一个测试文档，包含各种类型的图片。

## 在线图片示例
![在线图片](https://via.placeholder.com/300x200.png?text=Online+Image)

## 本地图片示例（如果存在）
![本地图片](./local_image.jpg)

## HTML格式图片
<img src="https://via.placeholder.com/400x300.png?text=HTML+Image" alt="HTML图片">

## 图表示例
![图1-1 TFEMJG-2 型 智能节水系统安装与运维综合实训平台](https://via.placeholder.com/600x400.png?text=System+Platform)

处理后，这些图片路径将被替换为FastGPT可访问的本地路径。
"""
    
    with open("test_document.md", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("已创建测试文件: test_document.md")
    return "test_document.md"

def main():
    """主函数"""
    print("FastGPT图片处理器使用示例")
    print("=" * 50)
    
    # 创建测试文件
    test_file = create_test_markdown()
    
    # 处理测试文件
    processor = FastGPTImageProcessor(
        host_image_dir="/home/<USER>/llm_project/fastgpt/images",
        web_access_path="/static/images",
        fastgpt_domain=""
    )
    
    print(f"\n处理测试文件: {test_file}")
    success = processor.process_single_markdown(test_file)
    
    if success:
        print("✅ 测试文件处理成功！")
        print("\n查看处理后的文件内容:")
        with open(test_file, "r", encoding="utf-8") as f:
            print(f.read())
    else:
        print("❌ 测试文件处理失败")
    
    print("\n" + "=" * 50)
    print("使用说明:")
    print("1. 确保Docker配置中已添加图片目录映射")
    print("2. 确保图片存储目录存在且有写权限")
    print("3. 修改脚本中的路径为您的实际路径")
    print("4. 运行处理脚本")
    print("\nDocker重启命令:")
    print("docker-compose down && docker-compose up -d")

if __name__ == "__main__":
    main()
