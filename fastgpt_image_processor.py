#!/usr/bin/env python3
"""
FastGPT图片处理器
用于将Markdown文件中的图片路径替换为FastGPT可访问的本地路径
"""

import os
import shutil
import hashlib
from typing import Tuple, Optional
from pdfdeal.file_tools import md_replace_imgs, mds_replace_imgs


class FastGPTImageProcessor:
    def __init__(
        self, 
        host_image_dir="/home/<USER>/llm_project/fastgpt/images",
        container_image_path="/app/static/images",
        web_access_path="/static/images",
        fastgpt_domain="http://localhost:3000"
    ):
        """
        初始化FastGPT图片处理器
        
        Args:
            host_image_dir: 宿主机图片存储目录
            container_image_path: 容器内图片路径
            web_access_path: Web访问路径前缀
            fastgpt_domain: FastGPT域名（可选，用于生成完整URL）
        """
        self.host_image_dir = host_image_dir
        self.container_image_path = container_image_path
        self.web_access_path = web_access_path
        self.fastgpt_domain = fastgpt_domain.rstrip('/')
        
        # 确保图片目录存在
        os.makedirs(host_image_dir, exist_ok=True)
        print(f"图片存储目录: {host_image_dir}")
    
    def _generate_unique_filename(self, original_path: str, md_filename: str) -> str:
        """
        生成唯一的文件名，避免冲突
        
        Args:
            original_path: 原始文件路径
            md_filename: Markdown文件名
            
        Returns:
            唯一的文件名
        """
        # 获取文件扩展名
        _, ext = os.path.splitext(original_path)
        
        # 使用文件内容的MD5作为文件名（如果是本地文件）
        if os.path.exists(original_path):
            with open(original_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
        else:
            # 如果是URL，使用URL的MD5
            file_hash = hashlib.md5(original_path.encode()).hexdigest()
        
        # 生成格式: md文件名_hash值.扩展名
        base_md_name = os.path.splitext(md_filename)[0]
        return f"{base_md_name}_{file_hash}{ext}"
    
    def _copy_to_fastgpt_dir(self, source_path: str, target_filename: str) -> Tuple[str, bool]:
        """
        将图片复制到FastGPT图片目录
        
        Args:
            source_path: 源文件路径
            target_filename: 目标文件名
            
        Returns:
            (web_url, success_flag)
        """
        try:
            target_path = os.path.join(self.host_image_dir, target_filename)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            # 生成Web访问URL
            web_url = f"{self.web_access_path}/{target_filename}"
            
            # 如果设置了域名，生成完整URL
            if self.fastgpt_domain:
                web_url = f"{self.fastgpt_domain}{web_url}"
            
            print(f"图片已复制: {source_path} -> {target_path}")
            print(f"Web访问路径: {web_url}")
            
            return web_url, True
            
        except Exception as e:
            print(f"复制图片失败: {source_path} -> {e}")
            return str(e), False
    
    def create_fastgpt_uploader(self, md_filename: str):
        """
        创建适用于FastGPT的图片上传函数
        
        Args:
            md_filename: Markdown文件名
            
        Returns:
            上传函数
        """
        def fastgpt_uploader(local_file_path: str, remote_file_name: str) -> Tuple[str, bool]:
            """
            FastGPT图片上传函数
            
            Args:
                local_file_path: 本地文件路径
                remote_file_name: 远程文件名（由pdfdeal生成）
                
            Returns:
                (web_url, success_flag)
            """
            # 跳过网络URL
            if local_file_path.startswith(('http://', 'https://')):
                print(f"跳过网络图片: {local_file_path}")
                return local_file_path, False
            
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(local_file_path):
                # 假设相对于当前工作目录
                local_file_path = os.path.abspath(local_file_path)
            
            # 检查文件是否存在
            if not os.path.exists(local_file_path):
                print(f"文件不存在: {local_file_path}")
                return f"文件不存在: {local_file_path}", False
            
            # 生成唯一文件名
            unique_filename = self._generate_unique_filename(local_file_path, md_filename)
            
            # 复制到FastGPT目录
            return self._copy_to_fastgpt_dir(local_file_path, unique_filename)
        
        return fastgpt_uploader
    
    def process_single_markdown(
        self, 
        md_file_path: str, 
        download_online_images: bool = True,
        temp_dir: str = "./temp_images"
    ) -> bool:
        """
        处理单个Markdown文件
        
        Args:
            md_file_path: Markdown文件路径
            download_online_images: 是否下载在线图片
            temp_dir: 临时目录
            
        Returns:
            处理是否成功
        """
        print(f"\n开始处理Markdown文件: {md_file_path}")
        
        md_filename = os.path.basename(md_file_path)
        success = True
        
        # 第一步：如果需要，先下载在线图片到临时目录
        if download_online_images:
            print("第一步：下载在线图片...")
            success = md_replace_imgs(
                mdfile=md_file_path,
                replace="local",
                outputpath=temp_dir,
                relative=False,
                threads=5
            )
            
            if not success:
                print("下载在线图片时出现错误，但继续处理本地图片...")
        
        # 第二步：将所有图片复制到FastGPT目录并更新路径
        print("第二步：处理图片路径...")
        uploader = self.create_fastgpt_uploader(md_filename)
        
        final_success = md_replace_imgs(
            mdfile=md_file_path,
            replace=uploader,
            path_style=True,
            uuid_rename=False  # 我们自己处理文件名
        )
        
        # 清理临时目录
        if download_online_images and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"已清理临时目录: {temp_dir}")
            except Exception as e:
                print(f"清理临时目录失败: {e}")
        
        print(f"处理完成: {md_file_path} - {'成功' if final_success else '部分失败'}")
        return final_success and success
    
    def process_multiple_markdowns(
        self, 
        md_directory: str,
        download_online_images: bool = True,
        threads: int = 2
    ) -> Tuple[list, list, bool]:
        """
        批量处理Markdown文件
        
        Args:
            md_directory: Markdown文件目录
            download_online_images: 是否下载在线图片
            threads: 并发线程数
            
        Returns:
            (成功文件列表, 失败文件列表, 整体是否成功)
        """
        print(f"\n开始批量处理目录: {md_directory}")
        
        # 如果需要下载在线图片，先批量下载
        if download_online_images:
            print("批量下载在线图片...")
            mds_replace_imgs(
                path=md_directory,
                replace="local",
                outputpath="./temp_images_batch",
                relative=False,
                threads=threads,
                down_load_threads=3
            )
        
        # 然后批量处理图片路径
        print("批量处理图片路径...")
        
        # 创建一个通用的上传函数
        def batch_uploader(local_file_path: str, remote_file_name: str) -> Tuple[str, bool]:
            # 从remote_file_name推断md文件名
            md_name = remote_file_name.split('_')[0] if '_' in remote_file_name else "unknown"
            uploader = self.create_fastgpt_uploader(f"{md_name}.md")
            return uploader(local_file_path, remote_file_name)
        
        success_files, failed_files, overall_success = mds_replace_imgs(
            path=md_directory,
            replace=batch_uploader,
            path_style=True,
            uuid_rename=False,
            threads=threads
        )
        
        # 清理批量临时目录
        if download_online_images and os.path.exists("./temp_images_batch"):
            try:
                shutil.rmtree("./temp_images_batch")
                print("已清理批量临时目录")
            except Exception as e:
                print(f"清理批量临时目录失败: {e}")
        
        print(f"批量处理完成: 成功 {len(success_files)}, 失败 {len(failed_files)}")
        return success_files, failed_files, overall_success


def main():
    """主函数示例"""
    # 初始化处理器
    processor = FastGPTImageProcessor(
        host_image_dir="/home/<USER>/llm_project/fastgpt/images",
        web_access_path="/static/images",
        fastgpt_domain=""  # 留空使用相对路径
    )
    
    # 处理单个文件示例
    # processor.process_single_markdown("example.md")
    
    # 批量处理示例
    # success, failed, overall = processor.process_multiple_markdowns("./markdown_docs/")
    
    print("FastGPT图片处理器已准备就绪")
    print("使用示例:")
    print("processor.process_single_markdown('your_file.md')")
    print("processor.process_multiple_markdowns('./your_markdown_directory/')")


if __name__ == "__main__":
    main()
