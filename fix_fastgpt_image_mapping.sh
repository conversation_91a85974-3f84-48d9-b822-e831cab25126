#!/bin/bash

# FastGPT图片映射修复脚本

echo "=========================================="
echo "FastGPT 图片映射修复脚本"
echo "=========================================="

# 配置变量
HOST_IMAGE_DIR="/home/<USER>/llm_project/fastgpt/images"
IMAGE_FILE="1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg"
CONTAINER_NAME="fastgpt"

echo ""
echo "1. 检查当前Docker Compose配置..."

# 检查docker-compose.yml是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 当前目录没有找到docker-compose.yml文件"
    echo "   请切换到包含docker-compose.yml的目录"
    exit 1
fi

# 检查是否已有卷映射配置
if grep -q "/home/<USER>/llm_project/fastgpt/images:/app/static/images" docker-compose.yml; then
    echo "✅ 发现图片目录映射配置"
else
    echo "❌ 未发现图片目录映射配置"
    echo ""
    echo "2. 备份当前配置..."
    cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份为: docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
    
    echo ""
    echo "3. 添加图片目录映射..."
    
    # 检查fastgpt服务的volumes部分
    if grep -A 20 "fastgpt:" docker-compose.yml | grep -q "volumes:"; then
        echo "   在现有volumes部分添加映射..."
        # 在volumes部分添加新的映射
        sed -i '/fastgpt:/,/environment:/{ /volumes:/a\      - /home/<USER>/llm_project/fastgpt/images:/app/static/images
        }' docker-compose.yml
    else
        echo "   创建新的volumes部分..."
        # 在fastgpt服务中添加volumes部分
        sed -i '/fastgpt:/,/environment:/{ /restart: always/a\    volumes:\n      - ./config.json:/app/data/config.json\n      - /home/<USER>/llm_project/fastgpt/images:/app/static/images
        }' docker-compose.yml
    fi
    
    echo "✅ 已添加图片目录映射配置"
fi

echo ""
echo "4. 重启FastGPT服务..."
echo "   停止服务..."
docker-compose down

echo "   等待服务完全停止..."
sleep 5

echo "   启动服务..."
docker-compose up -d

echo "   等待服务启动..."
sleep 15

echo ""
echo "5. 验证配置..."

# 检查容器是否运行
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ FastGPT容器已启动"
    
    # 检查容器内文件
    if docker exec "$CONTAINER_NAME" sh -c "test -f /app/static/images/$IMAGE_FILE"; then
        echo "✅ 容器内图片文件存在"
        
        # 获取文件大小
        CONTAINER_SIZE=$(docker exec "$CONTAINER_NAME" sh -c "ls -la /app/static/images/$IMAGE_FILE" | awk '{print $5}')
        HOST_SIZE=$(ls -la "$HOST_IMAGE_DIR/$IMAGE_FILE" | awk '{print $5}')
        
        if [ "$CONTAINER_SIZE" = "$HOST_SIZE" ]; then
            echo "✅ 文件大小匹配 ($CONTAINER_SIZE 字节)"
        else
            echo "⚠️  文件大小不匹配 (宿主机: $HOST_SIZE, 容器: $CONTAINER_SIZE)"
        fi
    else
        echo "❌ 容器内图片文件不存在"
        echo "   请检查配置或手动调试"
    fi
    
    # 测试HTTP访问
    echo ""
    echo "6. 测试HTTP访问..."
    sleep 5  # 等待服务完全启动
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000/static/images/$IMAGE_FILE" 2>/dev/null)
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ HTTP访问成功 (状态码: $HTTP_STATUS)"
    else
        echo "❌ HTTP访问失败 (状态码: $HTTP_STATUS)"
        echo "   请等待几分钟让服务完全启动，然后重试"
    fi
    
else
    echo "❌ FastGPT容器启动失败"
    echo "   请检查日志: docker-compose logs fastgpt"
fi

echo ""
echo "=========================================="
echo "修复完成！"
echo ""
echo "📋 结果总结:"
echo "   容器状态: $(docker ps | grep -q "$CONTAINER_NAME" && echo "运行中" || echo "未运行")"
echo "   容器内文件: $(docker exec "$CONTAINER_NAME" sh -c "test -f /app/static/images/$IMAGE_FILE" 2>/dev/null && echo "存在" || echo "不存在")"
echo "   HTTP访问: $([ "$HTTP_STATUS" = "200" ] && echo "成功" || echo "失败")"
echo ""
echo "🔗 在FastGPT中使用的路径:"
echo "   /static/images/$IMAGE_FILE"
echo ""
echo "🌐 浏览器测试地址:"
echo "   http://localhost:3000/static/images/$IMAGE_FILE"
echo ""
echo "📝 如果仍有问题，请运行以下命令查看日志:"
echo "   docker-compose logs fastgpt"
echo "=========================================="
