#!/usr/bin/env python3
"""
测试FastGPT图片显示的脚本
"""

import os
import requests
import time

def test_image_access():
    """测试图片是否可以通过FastGPT访问"""
    
    # 配置信息
    host_image_path = "/home/<USER>/llm_project/fastgpt/images/1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg"
    fastgpt_url = "http://localhost:3000"
    web_image_path = "/static/images/1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg"
    full_image_url = f"{fastgpt_url}{web_image_path}"
    
    print("FastGPT 图片访问测试")
    print("=" * 50)
    
    # 1. 检查宿主机文件是否存在
    print(f"1. 检查宿主机文件: {host_image_path}")
    if os.path.exists(host_image_path):
        file_size = os.path.getsize(host_image_path)
        print(f"   ✅ 文件存在，大小: {file_size} 字节")
    else:
        print(f"   ❌ 文件不存在")
        return False
    
    # 2. 检查文件权限
    print(f"2. 检查文件权限")
    try:
        stat_info = os.stat(host_image_path)
        permissions = oct(stat_info.st_mode)[-3:]
        print(f"   权限: {permissions}")
        if int(permissions) >= 644:
            print(f"   ✅ 权限正常")
        else:
            print(f"   ⚠️  权限可能不足，建议设置为644")
    except Exception as e:
        print(f"   ❌ 无法检查权限: {e}")
    
    # 3. 测试HTTP访问
    print(f"3. 测试HTTP访问: {full_image_url}")
    try:
        response = requests.get(full_image_url, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ HTTP访问成功，状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"   Content-Length: {response.headers.get('Content-Length', 'Unknown')}")
        else:
            print(f"   ❌ HTTP访问失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 无法连接到FastGPT服务，请确认服务是否运行")
        return False
    except Exception as e:
        print(f"   ❌ HTTP访问出错: {e}")
        return False
    
    # 4. 生成测试Markdown
    print(f"4. 生成测试Markdown文件")
    markdown_content = f"""# FastGPT 图片显示测试

## 测试图片

![图1-1 TFEMJG-2 型 智能节水系统安装与运维综合实训平台]({web_image_path})

## 图片信息

- **原始路径**: `{host_image_path}`
- **Web访问路径**: `{web_image_path}`
- **完整URL**: `{full_image_url}`

## 测试说明

如果您在FastGPT中看到上面的图片正常显示，说明配置成功！

## 故障排除

如果图片不显示，请检查：

1. Docker映射是否正确
2. 文件权限是否正确
3. FastGPT服务是否正常运行
4. 路径是否正确

"""
    
    test_md_file = "fastgpt_image_test.md"
    with open(test_md_file, "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print(f"   ✅ 已生成测试文件: {test_md_file}")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n📋 结果总结:")
    print(f"   • 宿主机文件: {'存在' if os.path.exists(host_image_path) else '不存在'}")
    print(f"   • HTTP访问: 成功")
    print(f"   • 测试文件: {test_md_file}")
    
    print(f"\n🔗 在FastGPT中使用的路径:")
    print(f"   {web_image_path}")
    
    print(f"\n🌐 浏览器直接访问:")
    print(f"   {full_image_url}")
    
    return True

def check_docker_container():
    """检查Docker容器状态"""
    print("\n" + "=" * 50)
    print("Docker容器检查")
    
    try:
        import subprocess
        
        # 检查FastGPT容器是否运行
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=fastgpt", "--format", "table {{.Names}}\t{{.Status}}"],
            capture_output=True,
            text=True
        )
        
        if "fastgpt" in result.stdout:
            print("✅ FastGPT容器正在运行")
            
            # 检查容器内的图片目录
            check_result = subprocess.run(
                ["docker", "exec", "fastgpt", "ls", "-la", "/app/static/images/"],
                capture_output=True,
                text=True
            )
            
            if "1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg" in check_result.stdout:
                print("✅ 图片文件在容器内可见")
            else:
                print("❌ 图片文件在容器内不可见")
                print("请检查Docker卷映射配置")
        else:
            print("❌ FastGPT容器未运行")
            print("请启动FastGPT: docker-compose up -d")
            
    except FileNotFoundError:
        print("❌ Docker命令不可用")
    except Exception as e:
        print(f"❌ 检查Docker容器时出错: {e}")

def main():
    """主函数"""
    print("开始FastGPT图片显示测试...")
    
    # 测试图片访问
    success = test_image_access()
    
    # 检查Docker容器
    check_docker_container()
    
    if success:
        print(f"\n🎉 测试成功！")
        print(f"您可以在FastGPT中使用以下路径:")
        print(f"/static/images/1f77fc1e117fa0f9a732b337141d2165676cf65a2c17d7f8d496487b632de099.jpg")
    else:
        print(f"\n❌ 测试失败，请检查配置")

if __name__ == "__main__":
    main()
